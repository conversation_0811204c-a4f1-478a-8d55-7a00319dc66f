# Ultra-minimal production image using distroless
# This version provides the smallest possible image size

# Build stage
FROM node:20-alpine AS builder

WORKDIR /app

# Install dumb-init for proper signal handling
RUN apk add --no-cache dumb-init

# Copy package files first for better layer caching
COPY package.json yarn.lock ./

# Install all dependencies (including devDependencies for build)
RUN yarn --frozen-lockfile --silent --non-interactive

# Copy source code and configuration files
COPY . .

# Build the application
RUN yarn build

# Production dependencies stage
FROM node:20-alpine AS deps

WORKDIR /app

# Copy package files
COPY package.json yarn.lock ./

# Install only production dependencies
RUN yarn --frozen-lockfile --production --silent --non-interactive && \
    yarn cache clean

# Final production stage using distroless
FROM gcr.io/distroless/nodejs20-debian12 AS production

WORKDIR /app

# Copy production dependencies
COPY --from=deps /app/node_modules ./node_modules

# Copy built application from builder stage
COPY --from=builder /app/dist ./dist
COPY --from=builder /app/server.js ./
COPY --from=builder /app/index.html ./
COPY --from=builder /app/package.json ./

# Use non-root user (distroless default)
USER nonroot

EXPOSE 4000

CMD ["server.js"]
