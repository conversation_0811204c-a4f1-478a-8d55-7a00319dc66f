import { defineConfig } from "vite";
import react from "@vitejs/plugin-react-swc";
import tailwindcss from "@tailwindcss/vite";
import path from "path";
import svgr from "vite-plugin-svgr";

// https://vite.dev/config/
export default defineConfig({
  plugins: [
    react(),
    tailwindcss(),
    svgr({
      include: "**/*.svg?react",
    }),
  ],
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
  preview: {
    port: 4000,
    strictPort: true,
    allowedHosts: [
      "localhost",
      "0.0.0.0",
      "yiwucuanclub.com",
      "yiwucuanclub.kliks.it",
    ],
  },
  server: {
    port: 4000,
    strictPort: true,
    host: true,
    // origin: "http://0.0.0.0:4000",
  },
});
