{"name": "ycc", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "node server.js", "build": "npm run build:client && npm run build:server", "build:client": "tsc -b && vite build --ssrManifest --outDir dist/client", "build:server": "tsc -b && vite build --ssr src/entry-server.tsx --outDir dist/server", "serve": "cross-env NODE_ENV=production node server.js", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-slot": "^1.2.3", "@tailwindcss/vite": "^4.1.7", "axios": "^1.9.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "compression": "^1.8.0", "cross-env": "^7.0.3", "express": "^5.1.0", "lucide-react": "^0.513.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-helmet": "^6.1.0", "react-router-dom": "^7.6.1", "sirv": "^3.0.1", "swiper": "^11.2.8", "tailwind-merge": "^3.3.0", "tailwindcss": "^4.1.7"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/express": "4.17.21", "@types/node": "^22.15.21", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@types/react-helmet": "^6.1.11", "@vitejs/plugin-react-swc": "^3.9.0", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "tw-animate-css": "^1.3.0", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5", "vite-plugin-svgr": "^4.3.0"}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}