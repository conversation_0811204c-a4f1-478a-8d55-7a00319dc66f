import CardPrimaryComponent from "@/components/card/card-primary.component";
import { But<PERSON> } from "@/components/ui/button";

export default function ProductPage() {
  return (
    <main className="grid bg-alt-background">
      <section className="px-4 py-6 grid gap-6">
        <h1 className="text-large-title text-center font-bold">
          Se<PERSON><PERSON> yang <PERSON> Butuhkan untuk Sukses <PERSON>
        </h1>

        <h2 className="text-body-text">
          Satu platform, empat solusi praktis—pilih langkah terbaik Anda hari
          ini.
        </h2>

        <Button variant="primary" className="w-fit mx-auto">
          Lihat Semua Produk
        </Button>
      </section>

      <section className="px-4 py-6 grid gap-6">
        <div className="grid gap-3">
          <h2 className="text-large-title font-bold">Produk Unggulan</h2>

          <h3 className="text-small-title">
            Produk Kami Untuk <PERSON>!!
          </h3>
        </div>

        <div className="grid gap-3">
          <CardPrimaryComponent className="grid gap-6">
            <h2 className="text-large-title text-center font-bold">
              Tur Bisnis Yiwu
              <br />
              7H 6M
            </h2>

            <div className="bg-blue w-full h-[2px]" />

            <p className="text-large-title text-center font-bold">
              Rp 26.000.000 / org
            </p>

            <div className="bg-blue w-full h-[2px]" />

            <p className="text-sub-title">
              Trip sourcing langsung bersama mentor; kunjungi 5 district market
              & 3 pabrik, pelatihan negosiasi harga on‑the‑spot.
            </p>

            <h3 className="font-bold text-small-title">Benefit Utama:</h3>
            <ul className="list-disc list-outside ml-8 text-sub-title grid gap-2">
              <li>Pendamping lokal Mandarin‑Indonesia</li>
              <li>Workshop harga FOB vs CIF</li>
              <li>Lifetime komunitas importir YCC</li>
            </ul>

            <Button
              variant="primary"
              className="text-white"
              style={{
                fontSize: "var(--text-sub-title)",
              }}
            >
              Booking Sekarang
            </Button>
          </CardPrimaryComponent>

          <CardPrimaryComponent className="grid gap-6">
            <h2 className="text-large-title text-center font-bold">
              Kelas Panduan
              <br />
              Impor Yiwu Lengkap
            </h2>

            <div className="bg-blue w-full h-[2px]" />

            <p className="text-large-title text-center font-bold">
              Rp 977.000
              <br />
              <span className="text-secondary-gold">EARLY BIRD DISCOUNT</span>
            </p>

            <div className="bg-blue w-full h-[2px]" />

            <p className="text-sub-title">
              riset produk, hitung bea‑cukai, strategi channel penjualan.
            </p>

            <p className="text-sub-title">
              40 video HD • 8 modul • akses 12 bulan
            </p>

            <h3 className="font-bold text-small-title">BONUS:</h3>
            <ul className="list-disc list-outside ml-8 text-sub-title grid gap-2">
              <li>template kontrak pabrik </li>
              <li>checklist HS Code.</li>
            </ul>

            <Button
              variant="primary"
              className="text-white"
              style={{
                fontSize: "var(--text-sub-title)",
              }}
            >
              Booking Sekarang
            </Button>
          </CardPrimaryComponent>

          <CardPrimaryComponent className="grid gap-6">
            <h2 className="text-large-title text-center font-bold">
              Konsultan AI
              <br />
              Impor Yiwu
            </h2>

            <div className="bg-blue w-full h-[2px]" />

            <p className="text-large-title text-center font-bold">
              Rp 379.000 / Bulan
            </p>

            <div className="bg-blue w-full h-[2px]" />

            <p className="text-sub-title">
              bot cerdas HS‑code, tarif pajak, & supplier.
            </p>

            <h3 className="font-bold text-small-title">Fitur:</h3>
            <ul className="list-disc list-outside ml-8 text-sub-title grid gap-2">
              <li>
                Chat‑bot 24/7 (Bahasa Indonesia) untuk tanya regulasi, HS Code,
                & vendor
              </li>
              <li>Database 30k+ supplier Yiwu, update mingguan</li>
              <li>Ekspor rekomendasi ke CSV/Google Sheet</li>
            </ul>

            <Button
              variant="primary"
              className="text-white"
              style={{
                fontSize: "var(--text-sub-title)",
              }}
            >
              Coba 7 Hari Gratis
            </Button>
          </CardPrimaryComponent>

          <CardPrimaryComponent className="grid gap-6">
            <h2 className="text-large-title text-center font-bold">
              E-book Panduan
              <br />
              Impor Yiwu + Checklist
            </h2>

            <div className="bg-blue w-full h-[2px]" />

            <p className="text-large-title text-center font-bold">Rp 137.000</p>

            <div className="bg-blue w-full h-[2px]" />

            <p className="text-sub-title">
              20 halaman panduan praktis, siap download.
            </p>

            <h3 className="font-bold text-small-title">Apa yang Anda dapat:</h3>
            <ul className="list-disc list-outside ml-8 text-sub-title grid gap-2">
              <li>60 halaman strategi impor step‑by‑step</li>
              <li>Checklist biaya tersembunyi & vendor forwarder</li>
            </ul>

            <Button
              variant="primary"
              className="text-white"
              style={{
                fontSize: "var(--text-sub-title)",
              }}
            >
              Beli & Download
            </Button>
          </CardPrimaryComponent>
        </div>
      </section>

      <section className="px-4 py-6 grid gap-6">
        <div className="w-full aspect-square bg-disabled"></div>

        <h2 className="text-small-headline font-bold">
          Mengapa Memilih Yiwu Cuan Club?
        </h2>

        <table className="table-auto">
          <thead>
            <tr>
              <th className="px-2 py-1 text-description text-start">Fitur</th>
              <th className="px-2 py-1 text-description">Yiwu Cuan Club</th>
              <th className="px-2 py-1 text-description">Agen Lain</th>
            </tr>
          </thead>

          <tbody>
            <tr>
              <td className="px-2 py-1 border-t border-secondary-gold text-body-text">
                Pendamping Bahasa
              </td>
              <td className="px-2 py-1 border-t border-secondary-gold text-description text-center">
                ✔️
              </td>
              <td className="px-2 py-1 border-t border-secondary-gold text-description text-center">
                ❌
              </td>
            </tr>
            <tr>
              <td className="px-2 py-1 border-t border-secondary-gold text-body-text">
                Akses AI Supplier
              </td>
              <td className="px-2 py-1 border-t border-secondary-gold text-description text-center">
                ✔️
              </td>
              <td className="px-2 py-1 border-t border-secondary-gold text-description text-center">
                ❌
              </td>
            </tr>
            <tr>
              <td className="px-2 py-1 border-t border-secondary-gold text-body-text">
                Garansi Harga Hotel
              </td>
              <td className="px-2 py-1 border-t border-secondary-gold text-description text-center">
                ✔️
              </td>
              <td className="px-2 py-1 border-t border-secondary-gold text-description text-center">
                ❌
              </td>
            </tr>
            <tr>
              <td className="px-2 py-1 border-t border-secondary-gold text-body-text">
                Komunitas Lifetime
              </td>
              <td className="px-2 py-1 border-t border-secondary-gold text-description text-center">
                ✔️
              </td>
              <td className="px-2 py-1 border-t border-secondary-gold text-description text-center">
                ❌
              </td>
            </tr>
          </tbody>
        </table>
      </section>

      <section className="py-6 px-4 grid gap-6">
        <div className="grid gap-3">
          <h2 className="text-small-headline font-bold">
            Apa Kata Peserta Tur Kami?
          </h2>

          <h3 className="text-small-title">
            Peserta tur sudah membuktikan dan merasa puas dengan layanan kami!
          </h3>
        </div>

        {/* Carousel */}
        <div className="w-full aspect-[3/4] bg-disabled"></div>

        <Button
          variant="primary"
          style={{
            fontSize: "var(--text-sub-title)",
          }}
        >
          Mulai Sekarang
        </Button>
      </section>

      <section className="py-6 px-4 grid gap-6">
        <h2 className="text-headline text-secondary-gold text-center font-bold">
          GARANSI TANPA RESIKO
        </h2>

        <h3 className="text-large-title font-bold">
          Dapatkan 100% Refund dalam 7 Hari
        </h3>

        <p className="text-sub-title">
          Jika solusi kami tidak membantu bisnis impor Anda, cukup hubungi
          layanan pelanggan dan kami akan mengembalikan dana tanpa pertanyaan.
        </p>

        <Button
          variant="primary"
          style={{
            fontSize: "var(--text-sub-title)",
          }}
        >
          Pilih Solusi Sekarang
        </Button>
      </section>
    </main>
  );
}
