import { ImgTestimoni1, ImgTestimoni2, ImgTestimoni3 } from "@/lib/assets";
import { Swiper, SwiperSlide } from "swiper/react";
import { Autoplay } from "swiper/modules";

import "swiper/css";

export const TestimoniCarouselComponent = () => {
  return (
    <Swiper
      spaceBetween={4}
      slidesPerView={1}
      modules={[Autoplay]}
      autoplay={{
        delay: 2500,
        disableOnInteraction: false,
      }}
      onSlideChange={() => console.log("slide change")}
      onSwiper={(swiper) => console.log(swiper)}
      className="w-full"
    >
      <SwiperSlide>
        <img
          src={ImgTestimoni1}
          alt="Testimoni 1"
          className="w-full h-auto aspect-square object-cover"
          loading="lazy"
        />
      </SwiperSlide>
      <SwiperSlide>
        <img
          src={ImgTestimoni2}
          alt="Testimoni 2"
          className="w-full h-auto aspect-square object-cover"
          loading="lazy"
        />
      </SwiperSlide>
      <SwiperSlide>
        <img
          src={ImgTestimoni3}
          alt="Testimoni 3"
          className="w-full h-auto aspect-square object-cover"
          loading="lazy"
        />
      </SwiperSlide>
    </Swiper>
  );
};
