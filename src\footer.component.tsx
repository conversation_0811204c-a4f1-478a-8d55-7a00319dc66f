import {
  IcD<PERSON>rd,
  IcDot,
  IcInstagram,
  IcTelegram,
  IcTiktok,
  IcWhatsapp,
  IcYoutube,
  ImgKliksLogoSmall,
  ImgLogo,
} from "./lib/assets";

const FooterComponent = () => {
  return (
    <footer className="bg-white p-4 grid gap-6 items-center justify-center border-t border-secondary-gold">
      <img src={ImgLogo} alt="Logo" loading="lazy" className="mx-auto" />

      <div className="flex gap-3 mx-auto">
        <a
          href="about:to"
          target="_blank"
          rel="noreferrer"
          className="cursor-pointer"
        >
          <IcInstagram />
        </a>
        <a
          href="about:to"
          target="_blank"
          rel="noreferrer"
          className="cursor-pointer"
        >
          <IcTelegram />
        </a>
        <a
          href="about:to"
          target="_blank"
          rel="noreferrer"
          className="cursor-pointer"
        >
          <IcYoutube />
        </a>
        <a
          href="about:to"
          target="_blank"
          rel="noreferrer"
          className="cursor-pointer"
        >
          <IcTiktok />
        </a>
        <a
          href="about:to"
          target="_blank"
          rel="noreferrer"
          className="cursor-pointer"
        >
          <IcDiscord />
        </a>
        <a
          href="https://wa.me/6285124322311?text=Halo%20Yiwu%20Cuan%20Club%2C%20saya%20mau%20tanya%20soal%20bisnis%20import"
          target="_blank"
          rel="noreferrer"
          className="cursor-pointer"
        >
          <IcWhatsapp />
        </a>
      </div>

      <div className="flex flex-wrap items-center justify-center">
        <div className="flex gap-2 items-center pr-3 py-1 cursor-pointer">
          <IcDot />
          <p className="text-description">Beranda</p>
        </div>
        <div className="flex gap-2 items-center pr-3 py-1 cursor-pointer">
          <IcDot />
          <p className="text-description">Produk</p>
        </div>
        <div className="flex gap-2 items-center pr-3 py-1 cursor-pointer">
          <IcDot />
          <p className="text-description">Tentang Kami</p>
        </div>
        <div className="flex gap-2 items-center pr-3 py-1 cursor-pointer">
          <IcDot />
          <p className="text-description">Blog</p>
        </div>
        <div className="flex gap-2 items-center pr-3 py-1 cursor-pointer">
          <IcDot />
          <p className="text-description">Kebijakan Privasi</p>
        </div>
        <div className="flex gap-2 items-center pr-3 py-1 cursor-pointer">
          <IcDot />
          <p className="text-description">Ketentuan Layanan</p>
        </div>
      </div>

      <div className="mx-auto flex items-center py-2 w-full border-t border-secondary-gold justify-center gap-2">
        <p className="text-description text-center flex-none">©2025 Part of</p>
        <img
          src={ImgKliksLogoSmall}
          alt="Kliks"
          loading="lazy"
          className="flex-none"
        />
      </div>
    </footer>
  );
};

export default FooterComponent;
