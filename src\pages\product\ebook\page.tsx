import { Button } from "@/components/ui/button";
import { IcStar } from "@/lib/assets";

export default function ProductEbookPage() {
  return (
    <main className="grid bg-alt-background">
      <section className="px-4 py-6 grid gap-6">
        <div>
          <h2 className="text-large-title text-center font-bold text-secondary-gold">
            Diskon 70%
          </h2>
          <h1 className="text-large-title text-center font-bold">
            Kuasai Impor <PERSON>
          </h1>
        </div>

        <p className="text-body-text text-center">
          e‑book + checklist siap pakai!
        </p>

        <div className="grid w-full">
          <div className="flex justify-center items-center">
            <p className="text-small-title text-gold text-center line-through py-4">
              Rp 457.000
            </p>

            <div className="relative">
              <IcStar />
              <div className="absolute top-0 left-0 right-0 bottom-0 flex items-center justify-center text-body-text">
                <span className="font-bold text-white">40%</span>
              </div>
            </div>
          </div>
          <p className="text-small-headline text-secondary-gold text-center font-bold">
            Rp 137.000
          </p>
        </div>

        <div className="grid gap-2">
          <p className="text-sub-title text-blue text-center font-bold">
            Diskon Terbatas!
          </p>
          <Button
            variant="primary"
            style={{
              fontSize: "var(--text-sub-title)",
            }}
          >
            Dapatkan Sekarang
          </Button>

          <span className="text-body-text text-center">
            *Harga promo otomatis berakhir dalam{" "}
            <span className="font-bold">24:00:00</span>
          </span>
        </div>
      </section>

      <section className="px-4 py-6 grid gap-6">
        <h2 className="text-small-headline font-bold">
          Yang Anda <br /> Dapatkan
        </h2>

        <ul className="grid gap-3 text-sub-title list-disc list-outside ml-8 marker:text-blue">
          <li>
            <div className="grid">
              <h4 className="font-bold text-blue">📚 E‑book 50+ Halaman</h4>
              <p>
                Langkah legal, pembayaran, forwarding, negosiasi, dan tips
                inspeksi kualitas.
              </p>
            </div>
          </li>
          <li>
            <div className="grid">
              <h4 className="font-bold text-blue">
                ✅ Checklist Interaktif (Google Sheet)
              </h4>
              <p>
                Daftar tugas sebelum, selama, dan sesudah belanja Yiwu—tinggal
                centang.
              </p>
            </div>
          </li>
          <li>
            <div className="grid gap-2">
              <h4 className="font-bold text-blue">🎁 Bonus</h4>
              <ul className="list-[lower-alpha] list-outside ml-8 grid gap-2 marker:!text-black">
                <li>Akses grup Telegram importer</li>
                <li>Kupon –50% upgrade AI Konsultan (30 hari)</li>
                <li>
                  Kredit Rp 137rb jika naik ke Kelas Panduan Impor Yiwu Lengkap
                  dalam 7 hari.
                </li>
              </ul>
            </div>
          </li>
        </ul>
      </section>

      <section className="px-4 py-6 grid gap-6">
        <h2 className="text-small-headline font-bold">Kenapa Wajib Punya?</h2>

        <ul className="grid gap-3 text-sub-title list-disc list-outside ml-8 marker:text-blue">
          <li>
            <div className="grid">
              <h4 className="font-bold text-blue">Hemat Waktu</h4>
              <p>ringkas hasil riset 100+ jam dalam satu file.</p>
            </div>
          </li>
          <li>
            <div className="grid">
              <h4 className="font-bold text-blue">Bahasa Indonesia 100%</h4>
              <p>mudah dipraktikkan.</p>
            </div>
          </li>
          <li>
            <div className="grid">
              <h4 className="font-bold text-blue">Update Seumur Hidup</h4>
              <p>revisi regulasi otomatis terkirim via email.</p>
            </div>
          </li>
          <li>
            <div className="grid">
              <h4 className="font-bold text-blue">Proof‑based</h4>
              <p>disusun mentor yang sudah 3× ekspor‑impor Yiwu.</p>
            </div>
          </li>
        </ul>

        <Button
          variant="primary"
          style={{
            fontSize: "var(--text-sub-title)",
          }}
        >
          Download PDF Itenerary Lengkap
        </Button>
      </section>

      <section className="py-6 px-4 grid gap-6">
        <h2 className="text-small-headline font-bold">
          Kata Mereka Yang Sudah Beli...
        </h2>

        {/* Carousel */}
        <div className="w-full aspect-[3/4] bg-disabled"></div>
      </section>

      <section className="py-6 px-4 grid gap-6 bg-white">
        <p className="text-small-headline text-secondary-gold font-bold text-center">
          Harga promo 70%
        </p>

        <div className="grid gap-2">
          <p className="text-sub-title text-blue font-bold text-center">
            TINGGAL HARI INI!
          </p>
          <Button
            variant="primary"
            style={{
              fontSize: "var(--text-sub-title)",
            }}
          >
            Checkout Sekarang – Rp137.000
          </Button>
        </div>
      </section>
    </main>
  );
}
