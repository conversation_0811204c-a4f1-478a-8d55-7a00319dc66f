import { TestimoniCarouselComponent } from "@/components/carousel/testimoni-carousel.component";
import { But<PERSON> } from "@/components/ui/button";
import { IcBussiness, IcMarker } from "@/lib/assets";
import { Link } from "react-router";

export default function HomePage() {
  return (
    <main className="grid">
      <section className="py-6 px-4 grid gap-6 bg-alt-background">
        <iframe
          className="aspect-[9/16] h-auto w-full"
          src="https://www.youtube-nocookie.com/embed/TeGAowZjjUc?rel=0&amp;autoplay=1&amp;cc_load_policy=1&amp;controls=0&amp;showinfo=0"
          allowFullScreen
          allow="autoplay"
          loading="eager"
          title="Mau mulai bisnis import?"
        ></iframe>

        <h1 className="text-large-title text-center font-bold">
          Bawa Produk <PERSON>wu ke Bisnis Anda Lebih Cepat
        </h1>

        <p className="text-body-text">
          Dari tur sourcing langsung hingga alat AI & kursus online—Yiwu Cuan
          Club mempersingkat perjalanan impor Anda.
        </p>

        <Button variant="primary" className="w-fit mx-auto" asChild>
          <Link to="/product">Lihat Semua Produk</Link>
        </Button>
      </section>

      <section className="bg-background py-6 px-4 grid gap-6">
        <div className="grid gap-2 justify-items-center">
          <IcBussiness />

          <div className="grid">
            <p className="text-description text-center">Dipercaya oleh</p>
            <p className="text-body-text font-bold text-center">
              300+ Alumni Tour
            </p>
          </div>
        </div>
        <div className="grid gap-2 justify-items-center">
          <IcMarker />

          <div className="grid">
            <p className="text-description text-center">
              Peer Support & Networking
            </p>
            <p className="text-body-text font-bold text-center">
              2.000+ Member Komunitas
            </p>
          </div>
        </div>
        <div className="grid gap-2 justify-items-center">
          <IcBussiness />

          <div className="grid">
            <p className="text-description text-center">Terpercaya</p>
            <p className="text-body-text font-bold text-center">
              97% Peserta Merekomendasikan
            </p>
          </div>
        </div>
      </section>

      <section className="py-6 px-4 grid gap-6 bg-alt-background">
        <div className="grid gap-3">
          <h2 className="text-small-headline font-bold">Produk Unggulan</h2>
          <h3 className="text-sub-title">Produk Kami Untuk Kebutuhanmu!</h3>
        </div>

        <div className="grid gap-3">
          <div className="border border-blue rounded-lg py-5 px-4 bg-background grid gap-6">
            <h4 className="text-large-title font-bold text-center">
              Tur Bisnis Yiwu
              <br />
              7H 6M
            </h4>

            <p className="text-sub-title">
              Kunjungi 75.000 kios grosir & negosiasi langsung.
            </p>

            <Button variant="primary" asChild>
              <Link to="/product/tour">Booking Sekarang</Link>
            </Button>
          </div>
          <div className="border border-blue rounded-lg py-5 px-4 bg-background grid gap-6">
            <h4 className="text-large-title font-bold text-center">
              Kelas Panduan Impor
              <br />
              Yiwu Lengkap
            </h4>

            <p className="text-sub-title">
              {`Langkah demi langkah impor sukses < 14 hari.`}
            </p>

            <Button variant="primary" asChild>
              <Link to="/product/class">Ikuti Kelas</Link>
            </Button>
          </div>
          <div className="border border-blue rounded-lg py-5 px-4 bg-background grid gap-6">
            <h4 className="text-large-title font-bold text-center">
              Konsultan AI Impor Yiwu
            </h4>

            <p className="text-sub-title">
              Dapatkan HS‑code, pajak, & supplier dalam detik.
            </p>

            <Button variant="primary" asChild>
              <Link to="/product/consult">Coba Konsultasikan</Link>
            </Button>
          </div>
          <div className="border border-blue rounded-lg py-5 px-4 bg-background grid gap-6">
            <h4 className="text-large-title font-bold text-center">
              E-book Panduan Impor Yiwu + Checklist
            </h4>

            <p className="text-sub-title">
              20 halaman panduan praktis, siap download.
            </p>

            <Button variant="primary" asChild>
              <Link to="/product/ebook">Beli Ebook Sekarang</Link>
            </Button>
          </div>
        </div>
      </section>

      <section className="py-6 px-4 grid gap-6 bg-alt-background">
        <div className="w-full aspect-square bg-disabled"></div>

        <h2 className="text-small-headline font-bold">Mengapa Kami?</h2>

        <ul className="grid gap-6 text-sub-title list-disc list-outside ml-8 marker:text-blue">
          <li>
            <div className="grid">
              <h4 className="font-bold text-blue">Solusi 360°</h4>
              <p>Belajar, sourcing, hingga otomasi pesanan.</p>
            </div>
          </li>
          <li>
            <div className="grid">
              <h4 className="font-bold text-blue">Pendampingan Legal</h4>
              <p>Template kontrak, panduan bea cukai & pajak.</p>
            </div>
          </li>
          <li>
            <div className="grid">
              <h4 className="font-bold text-blue">Komunitas Aktif</h4>
              <p>Forum, webinar bulanan, program afiliasi.</p>
            </div>
          </li>
        </ul>
      </section>

      <section className="py-6 px-4 grid gap-6 bg-alt-background">
        <h2 className="text-small-headline font-bold">
          Cara Kerja Yiwu Cuan Club
        </h2>

        <ul className="grid gap-4 text-sub-title list-decimal list-outside ml-8 marker:text-blue marker:font-bold">
          <li>
            <div className="grid">
              <h4 className="font-bold text-blue">Pilih Produk</h4>
              <p>Tur / Kursus / AI / E‑book.</p>
            </div>
          </li>
          <li>
            <div className="grid">
              <h4 className="font-bold text-blue">Aksi Cepat</h4>
              <p>Checkout online atau isi form tur.</p>
            </div>
          </li>
          <li>
            <div className="grid">
              <h4 className="font-bold text-blue">Mulai Impor</h4>
              <p>Akses materi & dukungan komunitas.</p>
            </div>
          </li>
        </ul>
      </section>

      <section className="py-6 px-4 grid gap-6 bg-alt-background">
        <div className="grid gap-3">
          <h2 className="text-small-headline font-bold">
            Apa Kata Peserta Tur Kami?
          </h2>

          <h3 className="text-small-title">
            Peserta tur sudah membuktikan dan merasa puas dengan layanan kami!
          </h3>
        </div>

        <TestimoniCarouselComponent />

        <Button variant="primary" asChild>
          <Link to="/product/tour">Mulai Sekarang</Link>
        </Button>
      </section>
    </main>
  );
}
