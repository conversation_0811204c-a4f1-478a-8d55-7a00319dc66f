import { clsx, type ClassValue } from "clsx";
import { twMerge } from "tailwind-merge";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export const serializePhoneNumber = (phone: string): string => {
  const trimmed = phone.trim();

  if (trimmed.startsWith("0")) {
    // Jika mulai dengan 0 → ganti ke 62
    return "62" + trimmed.slice(1);
  } else if (trimmed.startsWith("62")) {
    // Sudah +62 → biarkan
    return trimmed;
  } else {
    // Tidak mulai 0 atau 62 → tambahkan 62 di depan
    return "62" + trimmed;
  }
};
