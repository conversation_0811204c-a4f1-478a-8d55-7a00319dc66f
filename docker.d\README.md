# Docker Optimization for React SSR Application

This directory contains optimized Docker configurations to reduce image size from ~2GB to under 200MB.

## Files

- `Dockerfile` - Main optimized Dockerfile with multi-stage build
- `Dockerfile.minimal` - Ultra-minimal version using distroless base image
- `.dockerignore` - Excludes unnecessary files from build context

## Optimizations Applied

### 1. Multi-stage Build
- **Build stage**: Installs all dependencies and builds the application
- **Production stage**: Only includes runtime dependencies and built artifacts

### 2. Production Dependencies Only
- Uses `yarn --production` to install only runtime dependencies
- Removes development dependencies, build tools, and cache files

### 3. Docker Context Optimization
- `.dockerignore` excludes `node_modules`, source files, and development artifacts
- Reduces build context from ~500MB to ~50MB

### 4. Layer Optimization
- Combines RUN commands to reduce layers
- Cleans up package manager cache in the same layer

### 5. Security Improvements
- Runs as non-root user
- Uses `dumb-init` for proper signal handling
- Includes health check

## Usage

### Standard Optimized Build
```bash
docker build -f docker.d/Dockerfile -t ycc-app:optimized .
```

### Ultra-minimal Build (Smallest Size)
```bash
docker build -f docker.d/Dockerfile.minimal -t ycc-app:minimal .
```

### Run Container
```bash
docker run -p 4000:4000 ycc-app:optimized
```

## Expected Image Sizes

- **Original**: ~2GB
- **Optimized**: ~150-200MB
- **Minimal**: ~100-150MB

## Build Performance Tips

1. **Use BuildKit** for faster builds:
   ```bash
   DOCKER_BUILDKIT=1 docker build -f docker.d/Dockerfile -t ycc-app .
   ```

2. **Multi-platform builds**:
   ```bash
   docker buildx build --platform linux/amd64,linux/arm64 -f docker.d/Dockerfile -t ycc-app .
   ```

## Troubleshooting

If you encounter issues with the minimal distroless image:
1. Use the standard optimized Dockerfile instead
2. Check that all required Node.js modules are included
3. Verify file permissions and paths

## Production Considerations

- The optimized Dockerfile includes health checks
- Uses proper signal handling with dumb-init
- Runs as non-root user for security
- Includes only necessary runtime files
