import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { Button } from "@/components/ui/button";

export default function ProductClassPage() {
  return (
    <main className="grid bg-alt-background">
      <section className="px-4 py-6 grid gap-6">
        <div>
          <h2 className="text-large-title text-center font-bold text-secondary-gold">
            Diskon 70%
          </h2>
          <h1 className="text-large-title text-center font-bold">
            Kuasai Impor <PERSON>wu
            <br />
            Dalam 4 Minggu
          </h1>
        </div>

        <div className="grid gap-1">
          <p className="text-small-title text-gold text-center line-through font-bold">
            Rp 3.257.000
          </p>
          <p className="text-small-headline text-secondary-gold text-center font-bold">
            Rp 3.257.000
          </p>

          <h2 className="text-body-text font-bold text-center">
            Aks<PERSON> seumur hidup + update gratis!
          </h2>
        </div>

        <Button variant="primary" className="w-fit mx-auto">
          Dapatkan Sekarang
        </Button>
      </section>

      <section className="px-4 py-6 grid gap-6">
        <div className="w-full aspect-square bg-disabled"></div>

        <h2 className="text-small-headline font-bold">
          Kenapa Kelas Ini Spesial?
        </h2>

        <ul className="grid gap-3 text-sub-title list-disc list-outside ml-8 marker:text-blue">
          <li>
            <div className="grid">
              <h4 className="font-bold text-blue">Modul A–Z</h4>
              <p>legalitas, pembayaran RMB, shipping, bea masuk.</p>
            </div>
          </li>
          <li>
            <div className="grid">
              <h4 className="font-bold text-blue">Studi Kasus Riil</h4>
              <p>analisa margin mainan, aksesori, kosmetik.</p>
            </div>
          </li>
          <li>
            <div className="grid">
              <h4 className="font-bold text-blue">Template Siap Pakai</h4>
              <p>invoice proforma, kontrak OEM, checklist QC.</p>
            </div>
          </li>
          <li>
            <div className="grid">
              <h4 className="font-bold text-blue">Bimbingan Komunitas</h4>
              <p>grup Telegram premium + sesi Q&A live.</p>
            </div>
          </li>
        </ul>
      </section>

      <section className="px-4 py-6 grid gap-6">
        <h2 className="text-small-headline font-bold">Isi Modul</h2>

        <ul className="grid gap-3 text-sub-title list-decimal list-outside ml-8 font-bold">
          <li>
            <p>Fundamental Impor Yiwu & Alur Logistics</p>
          </li>
          <li>
            <p>Riset Produk & Supplier Screening</p>
          </li>
          <li>
            <p>Negosiasi Harga & MOQ</p>
          </li>
          <li>
            <p>Pembayaran Aman (RMB, LC, Escrow)</p>
          </li>
          <li>
            <p>Regulasi & Bea Masuk Indonesia</p>
          </li>
          <li>
            <p>Forwarder, QC, & Pengiriman</p>
          </li>
          <li>
            <p>Strategi Pemasaran & Penjualan</p>
          </li>
        </ul>
      </section>

      <section className="px-4 py-6 grid gap-6 bg-white">
        <h2 className="text-center font-bold text-headline text-secondary-gold">
          BONUS PEMBELIAN
        </h2>

        <ul className="grid gap-3 text-sub-title list-decimal list-outside ml-8">
          <li>Konsultan AI Impor Yiwu 30 hari – Rp 379.000</li>
          <li>E‑book Panduan Impor Yiwu + Checklist – Rp 137.000</li>
        </ul>

        <p className="text-small-title font-bold">
          Anda hemat Rp516.000! Gratis dengan pembelian kelas!
        </p>

        <Button
          variant="primary"
          style={{
            fontSize: "var(--text-sub-title)",
          }}
        >
          Ikuti Kelas Sekarang
        </Button>
      </section>

      <section className="py-6 px-4 grid gap-6">
        <div className="grid gap-3">
          <h2 className="text-small-headline font-bold">
            Apa Kata Peserta Tur Kami?
          </h2>

          <h3 className="text-small-title">
            Peserta tur sudah membuktikan dan merasa puas dengan layanan kami!
          </h3>
        </div>

        {/* Carousel */}
        <div className="w-full aspect-[3/4] bg-disabled"></div>

        <Button
          variant="primary"
          style={{
            fontSize: "var(--text-sub-title)",
          }}
        >
          Mulai Sekarang
        </Button>
      </section>

      <section className="px-4 py-6 grid gap-6 bg-white">
        <h2 className="text-center font-bold text-headline text-secondary-gold">
          Siap Menjadi Importir Andal?
        </h2>

        <p className="text-small-title font-bold">
          Garansi uang kembali 7 hari jika materi tidak sesuai ekspektasi.
        </p>

        <Button
          variant="primary"
          style={{
            fontSize: "var(--text-sub-title)",
          }}
        >
          Beli Sekarang – Rp 977.000
        </Button>
      </section>

      <section className="px-4 py-6 grid gap-6">
        <h2 className="font-bold text-small-headline text-blue">F A Q</h2>

        <p className="text-body-text">
          Temukan jawaban atas beberapa pertanyaan umum mengenai layanan kami.
          Jika Anda memiliki pertanyaan lain, jangan ragu untuk menghubungi kami
          langsung.
        </p>

        <Accordion
          type="single"
          collapsible
          className="w-full"
          defaultValue="item-1"
        >
          <AccordionItem value="item-1">
            <AccordionTrigger className="text-sub-title font-bold">
              Apakah kelas ini rekaman atau live?
            </AccordionTrigger>
            <AccordionContent className="flex flex-col gap-4 text-balance text-body-text">
              Rekaman HD + sesi Q&A live bulanan.
            </AccordionContent>
          </AccordionItem>
          <AccordionItem value="item-2">
            <AccordionTrigger className="text-sub-title font-bold">
              Berapa lama akses materi?
            </AccordionTrigger>
            <AccordionContent className="flex flex-col gap-4 text-balance text-body-text">
              Lifetime.
            </AccordionContent>
          </AccordionItem>
          <AccordionItem value="item-3">
            <AccordionTrigger className="text-sub-title font-bold">
              Apakah butuh pengalaman impor?
            </AccordionTrigger>
            <AccordionContent className="flex flex-col gap-4 text-balance text-body-text">
              Tidak, modul disusun dari nol.
            </AccordionContent>
          </AccordionItem>
          <AccordionItem value="item-4">
            <AccordionTrigger className="text-sub-title font-bold">
              Bagaimana cara gunakan bonus AI?
            </AccordionTrigger>
            <AccordionContent className="flex flex-col gap-4 text-balance text-body-text">
              Voucher dikirim otomatis setelah checkout.
            </AccordionContent>
          </AccordionItem>
        </Accordion>
      </section>
    </main>
  );
}
