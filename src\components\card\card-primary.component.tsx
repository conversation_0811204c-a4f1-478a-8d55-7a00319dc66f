import { cn } from "@/lib/utils";
import type { ComponentProps } from "react";

interface IProps extends ComponentProps<"div"> {
  className?: string;
}

export default function CardPrimaryComponent({
  className,
  children,
  ...props
}: IProps) {
  return (
    <div
      className={cn(
        "border-[2px] border-blue rounded-lg py-5 px-4 bg-background",
        className
      )}
      {...props}
    >
      {children}
    </div>
  );
}
